package com.pugwoo.weixin_msg;

import com.pugwoo.weixin_msg.dto.ContactDTO;
import com.pugwoo.weixin_msg.dto.MsgDTO;
import com.pugwoo.weixin_msg.utils.MsgUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * 用于测试MsgUtils的各项功能
 */
public class TestMsgUtils {

    @Test
    public void testParse() {
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom",
                false, null);
        System.out.println(msgs.size());
    }

    @Test
    public void testParseMedia() {
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom",
                true, ListUtils.of(4135175694025292375L));
        ListUtils.sortAscNullFirst(msgs, MsgDTO::getMsgTime);
        for (MsgDTO msg : msgs) {
            System.out.println(msg.getMsgTime() + ",msgSvrID:" + msg.getMsgSvrID()
                    + ",type:" + msg.getType() + ",filePath:" + msg.getFilePath()
                    + ", thumb:" + msg.getThumbnailPath());
        }

        // 统计图片中，有filePath的数量和有thumb的数量
        System.out.println("total:" + msgs.size() + ",filePath:" +
                ListUtils.filter(msgs, o -> o.getFilePath() != null).size()
                + ",thumb:" + ListUtils.filter(msgs, o -> o.getThumbnailPath() != null).size()
                + ",none:" + ListUtils.filter(msgs, o -> o.getFilePath() == null && o.getThumbnailPath() == null).size());
    }

    @Test
    public void testGetContacts() {
        // 测试获取所有联系人
        List<ContactDTO> allContacts = MsgUtils.getContacts("E:\\weixin-msg\\User", null);
        System.out.println("总联系人或群数量: " + allContacts.size());

        // allContacts = ListUtils.filter(allContacts, o -> o.getNickName().contains("豆豆"));
        
        // 显示前5个联系人
        for (int i = 0; i < Math.min(5, allContacts.size()); i++) {
            ContactDTO contact = allContacts.get(i);
            System.out.println("联系人: " + contact.getUserName() + " - " + contact.getNickName());
        }
        
        // 测试通配符查询
        List<ContactDTO> wildcardContacts = MsgUtils.getContacts("E:\\weixin-msg\\User", "*chat*");
        System.out.println("包含'chat'的联系人数量: " + wildcardContacts.size());
        
        // 测试精确查询（如果有特定联系人）
        List<ContactDTO> specificContacts = MsgUtils.getContacts("E:\\weixin-msg\\User", "24507125117@chatroom");
        System.out.println("特定联系人查询结果数量: " + specificContacts.size());
        if (!specificContacts.isEmpty()) {
            System.out.println("找到联系人: " + specificContacts.get(0).getNickName());
        }
    }

}
